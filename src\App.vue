<template>
  <div class="app-container" :class="themeClass">
    <TitleBar />
    <div class="main-content">
      <SidebarNavigation v-if="isAppReady" />
      <div class="content-container">
        <div v-if="!isAppReady" class="app-loading">
          <div class="loading-spinner"></div>
          <p class="loading-text">Loading Noti...</p>
        </div>
        <router-view v-else />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onBeforeUnmount, computed, ref } from 'vue'
import SidebarNavigation from './components/common/SidebarNavigation.vue'
import TitleBar from './components/common/TitleBar.vue'
import { useTimerStore } from './stores/timerStore'
import { useSettingsStore } from './stores/settingsStore'
import { resolveTheme } from './utils/themeUtils'
import { useGlobalKeybinds } from './composables/useGlobalKeybinds'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'App',
  components: {
    SidebarNavigation,
    TitleBar
  },
  setup() {
    const timerStore = useTimerStore()
    const settingsStore = useSettingsStore()
    const { setup: setupKeybinds, cleanup: cleanupKeybinds } = useGlobalKeybinds()
    const router = useRouter()

    // App loading state
    const isAppReady = ref(false)

    // Computed theme class for the app container
    const themeClass = computed(() => {
      const resolvedTheme = resolveTheme(settingsStore.currentTheme)
      return `theme-${resolvedTheme}`
    })

    // Initialize stores when app mounts
    onMounted(async () => {
      try {
        console.log('🚀 App initialization started...')

        // Initialize settings first (this will apply the theme)
        await settingsStore.initialize()
        console.log('✅ Settings store initialized')

        // Initialize timer store (loads active session and settings)
        await timerStore.initialize()
        console.log('✅ Timer store initialized')

        // Initialize global keybinds system
        setupKeybinds()
        console.log('✅ Global keybinds initialized')

        // Wait for initial route to be ready (dashboard components to load)
        await router.isReady()
        console.log('✅ Router ready')

        // Add a small delay to ensure dashboard components have time to mount
        await new Promise(resolve => setTimeout(resolve, 100))

        // Mark app as ready - this will show the sidebar and hide loading
        isAppReady.value = true
        console.log('✅ App fully initialized and ready')
      } catch (error) {
        console.error('❌ Failed to initialize app stores:', error)
        // Even if there's an error, show the app to prevent infinite loading
        isAppReady.value = true
      }
    })

    // Cleanup when app unmounts
    onBeforeUnmount(() => {
      try {
        // Clean up keybinds system
        cleanupKeybinds()

        // Clean up timer intervals and save state
        timerStore.cleanup()

        // Clean up settings store (theme watchers)
        settingsStore.cleanup()

        console.log('App cleanup completed')
      } catch (error) {
        console.error('Error during app cleanup:', error)
      }
    })

    return {
      timerStore,
      settingsStore,
      themeClass,
      isAppReady
    }
  }
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  height: 100vh;
  overflow: hidden;
  /* Prevent text selection by default throughout the app */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.content-container {
  flex: 1;
  overflow: auto;
  padding: 0px;
  background-color: var(--color-bg-primary);
  position: relative;  /* Add this line */
  z-index: 1;         /* Add this line */
}

/* App loading state */
.app-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border-primary);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--color-text-secondary);
  margin: 0;
}

/* Ensure all interactive elements are non-draggable */
button, a, input, textarea, select, [role="button"] {
  -webkit-app-region: no-drag;
}

/* Reset any potential imported defaults from style.css */
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  max-width: none;
  text-align: left;
}

/* Class to enable text selection for editable elements */
.user-select-text, input, textarea {
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Style rules for modal components */
.modal-header, .modal-footer {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Allow text selection in text fields and selectable content */
.modal-content .selectable-text,
.modal-content input,
.modal-content textarea {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}
</style>